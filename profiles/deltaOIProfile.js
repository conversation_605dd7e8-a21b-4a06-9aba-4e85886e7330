window.deltaOiProfileManager = (() => {
  // Constants and Defaults
  const priceRangeMultiplier = 2;
  const DEFAULT_CONFIG = {
    priceRangeMultiplier,
    priceRange: (() => {
      const savedVPLines = localStorage.getItem("volumeProfileLines");
      return savedVPLines
        ? parseInt(savedVPLines) * priceRangeMultiplier
        : 1600;
    })(),
    barWidth: 1.0,
    position: 0.1,
    alignLeft: true,
    colors: {
      forcedClose: "rgba(255, 0, 0, 0.7)", // Red for forced closing bars
      neutral: "rgba(150, 150, 150, 0.5)",
    },
    forcedCloseThreshold: 0.00001,
    priceChangeThreshold: 0.002,
    showMedian: false,
    visible: true,
    liveUpdate: true,
    maxBars: 12000,
  };

  const profiles = new Map();
  const deltaOiProfileCache = new WeakMap();

  // Internal Modules
  const utils = {
    debounce(func, wait) {
      let timeout;
      const debounced = function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
      };
      debounced.cancel = () => clearTimeout(timeout);
      return debounced;
    },
    optimizeDataForCalculation(priceData, maxBars) {
      if (priceData.length <= maxBars) return priceData;
      return priceData.slice(-maxBars);
    },
  };

  const dataFetcher = {
    async fetchDataInParallel(profile) {
      if (profile.dataLoaded || profile.fetchingData) return;
      profile.fetchingData = true;

      const chartState = profile.chartState;
      const symbol = `${chartState.config.ticker.symbol}USDT`;
      const interval = "5min";
      const endTime = Date.now();
      const totalDuration = 6000 * 5 * 60 * 1000;
      const numChunks = 24;
      const chunkSize = totalDuration / numChunks;
      const timeRanges = Array.from({ length: numChunks }, (_, i) => ({
        start: endTime - (i + 1) * chunkSize,
        end: i === 0 ? endTime : endTime - i * chunkSize,
      }));

      async function fetchWithRetry(url, retries = 3, delay = 1000) {
        for (let i = 0; i < retries; i++) {
          try {
            const response = await fetch(url, { mode: "cors" });
            if (response.ok) return response;
            throw new Error("Fetch failed");
          } catch (error) {
            if (i === retries - 1) throw error;
            await new Promise((resolve) =>
              setTimeout(resolve, delay * Math.pow(2, i)),
            );
          }
        }
      }

      const fetchChunk = async (startTime, endTime) => {
        const apiUrl = `https://api.bybit.com/v5/market/open-interest?category=linear&symbol=${symbol}&intervalTime=${interval}&startTime=${startTime}&endTime=${endTime}&limit=1000`;
        let response;
        try {
          response = await fetchWithRetry(apiUrl);
        } catch {
          try {
            const proxyUrl = `https://corsproxy.io/?${encodeURIComponent(apiUrl)}`;
            response = await fetchWithRetry(proxyUrl);
          } catch {
            const altProxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(apiUrl)}`;
            response = await fetchWithRetry(altProxyUrl);
          }
        }
        if (!response.ok) return [];
        const data = await response.json();
        return data.retCode === 0 ? data.result.list : [];
      };

      const chunkResults = await Promise.all(
        timeRanges.map((range) => fetchChunk(range.start, range.end)),
      );
      let allData = [].concat(...chunkResults);
      const uniqueData = [];
      const timestampSet = new Set();
      allData.forEach((item) => {
        if (!timestampSet.has(item.timestamp)) {
          timestampSet.add(item.timestamp);
          uniqueData.push(item);
        }
      });
      uniqueData.sort((a, b) => parseInt(a.timestamp) - parseInt(b.timestamp));
      const processedData = [];
      const timeMap = new Map();
      uniqueData.forEach((item) => {
        const timestamp = parseInt(item.timestamp) / 1000;
        if (timeMap.has(timestamp)) return;
        const priceData = chartState.data.priceData || [];
        const closestBar = dataFetcher.findClosestBar(priceData, timestamp);
        if (closestBar) {
          const openInterestValue = parseFloat(item.openInterest);
          processedData.push({
            time: closestBar.time,
            price: closestBar.close,
            openInterest: openInterestValue,
            priceChange: (closestBar.close - closestBar.open) / closestBar.open,
            buyFlow: 0,
            sellFlow: 0,
            hasOrderFlow: false,
          });
          timeMap.set(timestamp, true);
        }
      });
      processedData.sort((a, b) => a.time - b.time);
      const finalData = processedData.slice(-12000);
      chartState.data.openInterestData = finalData;
      profile.data = calculator.calculateDeltaOiProfile(
        chartState.data.priceData.slice(-profile.config.maxBars),
        chartState.data.openInterestData,
        profile.config,
      );
      profile.dataLoaded = true;
      profile.fetchingData = false;
      profile.isDirty = true; // Set dirty flag
      manager.updateProfile(chartState.config.ticker.symbol);
    },
    findClosestBar(priceBars, timestamp) {
      if (!priceBars || !priceBars.length) return null;
      let closestBar = null;
      let minDistance = Infinity;
      for (const bar of priceBars) {
        const distance = Math.abs(bar.time - timestamp);
        if (distance < minDistance) {
          minDistance = distance;
          closestBar = bar;
        }
      }
      return closestBar;
    },
  };

  const calculator = {
    computeRollingAverageVolume(priceData, windowSize) {
      if (!Array.isArray(priceData) || priceData.length === 0) return [];
      const avgVolumes = [];
      for (let i = 0; i < priceData.length; i++) {
        const start = Math.max(0, i - windowSize + 1);
        const windowSlice = priceData.slice(start, i + 1);
        const sum = windowSlice.reduce(
          (acc, bar) => acc + (bar.volume || 0),
          0,
        );
        avgVolumes.push(sum / windowSlice.length);
      }
      return avgVolumes;
    },
    computeRollingStdev(data, windowSize) {
      const stdevs = [];
      for (let i = windowSize - 1; i < data.length; i++) {
        const window = data.slice(i - windowSize + 1, i + 1);
        const mean = window.reduce((a, b) => a + b, 0) / window.length;
        const stdev = Math.sqrt(
          window.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / window.length,
        );
        stdevs.push(stdev);
      }
      return stdevs;
    },
    detectOiEvents(pointsInBin, priceStep, config) {
      const oiRocs = [];
      for (let i = 1; i < pointsInBin.length; i++) {
        oiRocs.push(
          pointsInBin[i].openInterest - pointsInBin[i - 1].openInterest,
        );
      }
      const stdevs = this.computeRollingStdev(oiRocs, 30);
      const events = {
        forcedClose: 0,
        forcedCloseCount: 0,
        forcedOpen: 0,
        forcedOpenCount: 0,
        totalOiRoc: 0,
      };

      for (let i = 1; i < pointsInBin.length; i++) {
        const oiRoc = oiRocs[i - 1];
        events.totalOiRoc += oiRoc;
        const adaptiveThreshold =
          i - 1 >= 29 && stdevs[i - 30] > 0
            ? stdevs[i - 30] * 2
            : config.forcedCloseThreshold *
              (Math.abs(pointsInBin[i - 1].openInterest) || 1);

        if (oiRoc < -adaptiveThreshold) {
          events.forcedClose += Math.abs(oiRoc);
          events.forcedCloseCount += 1;
        } else if (oiRoc > adaptiveThreshold) {
          events.forcedOpen += Math.abs(oiRoc);
          events.forcedOpenCount += 1;
        }
      }
      return events;
    },
    calculateDeltaOiProfile(priceData, openInterestData, config) {
      try {
        if (deltaOiProfileCache.has(priceData)) {
          const cached = deltaOiProfileCache.get(priceData);
          if (
            cached.configHash === JSON.stringify(config) &&
            cached.oiData === openInterestData
          )
            return cached.result;
        }

        if (!Array.isArray(priceData) || !priceData.length) return null;
        if (!Array.isArray(openInterestData)) openInterestData = [];
        config = { ...DEFAULT_CONFIG, ...config };

        const computeMinMax =
          window.mathUtils?.computeRollingMinMax ||
          (window.baseProfile && window.baseProfile.getMinMax);
        if (!computeMinMax) return null;

        const optimizedData = utils.optimizeDataForCalculation(
          priceData,
          config.maxBars,
        );
        if (!optimizedData || !optimizedData.length) return null;

        let minPrice = Infinity;
        let maxPrice = -Infinity;
        optimizedData.forEach((bar) => {
          minPrice = Math.min(minPrice, bar.low);
          maxPrice = Math.max(maxPrice, bar.high);
        });
        if (
          minPrice === Infinity ||
          maxPrice === -Infinity ||
          minPrice >= maxPrice
        )
          return null;

        const padding = (maxPrice - minPrice) * 0.05;
        minPrice -= padding;
        maxPrice += padding;

        const priceStep = (maxPrice - minPrice) / config.priceRange;
        const priceLevels = Array.from(
          { length: config.priceRange },
          (_, i) => ({
            price: minPrice + i * priceStep + priceStep / 2,
            forcedClose: 0,
            forcedCloseCount: 0,
            forcedOpen: 0,
            forcedOpenCount: 0,
            totalOiRoc: 0,
          }),
        );

        if (openInterestData.length) {
          const sortedOIData = [...openInterestData].sort(
            (a, b) => a.time - b.time,
          );
          priceLevels.forEach((level) => {
            const pointsInBin = sortedOIData.filter((oiPoint) => {
              if (!oiPoint.price || !oiPoint.openInterest) return false;
              return Math.abs(level.price - oiPoint.price) <= priceStep * 1.5;
            });
            pointsInBin.sort((a, b) => a.time - b.time);
            const events = this.detectOiEvents(pointsInBin, priceStep, config);
            level.forcedClose = events.forcedClose;
            level.forcedCloseCount = events.forcedCloseCount;
            level.forcedOpen = events.forcedOpen;
            level.forcedOpenCount = events.forcedOpenCount;
            level.totalOiRoc = events.totalOiRoc;
          });
        }

        const maxForcedClose = Math.max(
          1,
          ...priceLevels.map((level) => level.forcedClose),
        );
        const result = {
          levels: priceLevels,
          maxForcedClose,
          priceStep,
          minPrice,
          maxPrice,
        };
        deltaOiProfileCache.set(priceData, {
          configHash: JSON.stringify(config),
          oiData: openInterestData,
          result,
        });
        return result;
      } catch (error) {
        console.error("Error calculating OI ROC profile:", error);
        return null;
      }
    },
  };

  const renderer = {
    priceToY(price, height, profile, chartState) {
      const { priceChart, priceSeries } = chartState.chart;
      if (typeof priceSeries.priceToCoordinate === "function") {
        return priceSeries.priceToCoordinate(price) || 0;
      }
      const priceScale = priceSeries.priceScale();
      if (!priceScale) return 0;
      let topPrice, bottomPrice;
      if (priceScale.priceRange) {
        const range = priceScale.priceRange();
        if (!range) return 0;
        topPrice = range.maxValue?.() ?? range.max;
        bottomPrice = range.minValue?.() ?? range.min;
      } else if (priceScale.getVisibleRange) {
        const range = priceScale.getVisibleRange();
        if (!range) return 0;
        topPrice = range.maxValue;
        bottomPrice = range.minValue;
      } else {
        const visibleBars = priceChart.timeScale().getVisibleRange();
        if (!visibleBars) return height / 2;
        const now = Date.now();
        const cacheExpiry = 500;
        if (
          profile.priceRangeCache.timestamp + cacheExpiry > now &&
          profile.priceRangeCache.height === height
        ) {
          topPrice = profile.priceRangeCache.topPrice;
          bottomPrice = profile.priceRangeCache.bottomPrice;
        } else {
          const visibleData = chartState.data.priceData.filter(
            (bar) => bar.time >= visibleBars.from && bar.time <= visibleBars.to,
          );
          if (!visibleData.length) return height / 2;
          topPrice = Math.max(...visibleData.map((bar) => bar.high));
          bottomPrice = Math.min(...visibleData.map((bar) => bar.low));
          const padding = (topPrice - bottomPrice) * 0.1;
          topPrice += padding;
          bottomPrice -= padding;
          profile.priceRangeCache = {
            topPrice,
            bottomPrice,
            height,
            timestamp: Date.now(),
          };
        }
      }
      const priceDiff = topPrice - bottomPrice;
      return priceDiff === 0
        ? height / 2
        : height - ((price - bottomPrice) / priceDiff) * height;
    },
    drawDeltaOiProfile(profile) {
      try {
        const { deltaOiProfileCanvas, chartCanvas, chartState } = profile;
        if (!profile.dataLoaded) {
          const ctx = deltaOiProfileCanvas.getContext("2d");
          if (ctx) {
            if (
              chartCanvas.width !== deltaOiProfileCanvas.width ||
              chartCanvas.height !== deltaOiProfileCanvas.height
            ) {
              deltaOiProfileCanvas.width = chartCanvas.width;
              deltaOiProfileCanvas.height = chartCanvas.height;
            }
            ctx.clearRect(
              0,
              0,
              deltaOiProfileCanvas.width,
              deltaOiProfileCanvas.height,
            );
            const height = deltaOiProfileCanvas.height;
            const profileWidth = 80;
            const startX = 0;
            ctx.fillStyle = "rgba(15, 20, 26, 0.85)";
            ctx.fillRect(startX, 0, profileWidth, height);
            ctx.strokeStyle = "rgba(100, 100, 100, 0.7)";
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(startX + profileWidth, 0);
            ctx.lineTo(startX + profileWidth, height);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(startX, 0);
            ctx.lineTo(startX, height);
            ctx.stroke();
            ctx.fillStyle = "rgba(255, 255, 255, 0.7)";
            ctx.font = "9px sans-serif";
            ctx.textAlign = "center";
            ctx.fillText("ΔOI", startX + profileWidth / 2, height / 2);
            const now = Date.now();
            const phase = (now % 2000) / 2000;
            const radius = 10;
            const centerX = startX + profileWidth / 2;
            const centerY = height / 2 + 20;
            ctx.strokeStyle = "rgba(255, 255, 255, 0.7)";
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2 * phase);
            ctx.stroke();
          }
          return;
        }

        if (
          !profile.visible ||
          !profile.data?.levels ||
          !deltaOiProfileCanvas ||
          !chartCanvas
        )
          return;
        if (
          !Array.isArray(profile.data.levels) ||
          profile.data.levels.length === 0
        )
          return;

        if (
          chartCanvas.width !== deltaOiProfileCanvas.width ||
          chartCanvas.height !== deltaOiProfileCanvas.height
        ) {
          deltaOiProfileCanvas.width = chartCanvas.width;
          deltaOiProfileCanvas.height = chartCanvas.height;
        }
        const ctx = deltaOiProfileCanvas.getContext("2d");
        if (!ctx) return;

        ctx.clearRect(
          0,
          0,
          deltaOiProfileCanvas.width,
          deltaOiProfileCanvas.height,
        );
        const height = deltaOiProfileCanvas.height;
        const pixelRatio = window.devicePixelRatio || 1;
        const { levels } = profile.data;
        const { barWidth } = profile.config;
        const profileWidth = 80;
        const startX = 0;

        ctx.fillStyle = "rgba(15, 20, 26, 0.85)";
        ctx.fillRect(startX, 0, profileWidth, height);
        ctx.strokeStyle = "rgba(100, 100, 100, 0.7)";
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(startX + profileWidth, 0);
        ctx.lineTo(startX + profileWidth, height);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(startX, 0);
        ctx.lineTo(startX, height);
        ctx.stroke();

        ctx.save();
        ctx.scale(pixelRatio, pixelRatio);

        const priceData = profile.chartState?.data?.priceData || [];
        const maxForcedClose = Math.max(
          1,
          ...levels.map((level) => level.forcedClose),
        );

        function getDynamicColor(ratio) {
          const v = Math.round(40 + 180 * ratio);
          return `rgba(${v},${v},${v},0.8)`;
        }

        levels.forEach((level) => {
          const y = this.priceToY(level.price, height, profile, chartState);
          const nextPrice = level.price + profile.data.priceStep;
          const nextY = this.priceToY(nextPrice, height, profile, chartState);
          const barHeight = Math.max(1, Math.abs(nextY - y));
          const barStartX = startX;

          if (level.forcedClose === 0) return;
          const closeRatio = level.forcedClose / maxForcedClose;
          const barLength = profileWidth * barWidth * closeRatio * 0.9;
          ctx.fillStyle = getDynamicColor(closeRatio);
          ctx.globalAlpha = 0.8;
          ctx.fillRect(barStartX, y - barHeight / 2, barLength, barHeight);
          ctx.globalAlpha = 1.0;
        });

        ctx.fillStyle = "rgba(255, 255, 255, 0.9)";
        ctx.font = `${Math.round(10 * pixelRatio)}px sans-serif`;
        ctx.textAlign = "center";
        ctx.fillText("ΔOI", startX + profileWidth / 2, 20);

        ctx.strokeStyle = "rgba(100, 100, 100, 0.7)";
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(startX, 30);
        ctx.lineTo(startX + profileWidth, 30);
        ctx.stroke();
        ctx.restore();
      } catch (error) {
        console.error("Error drawing negative delta OI profile:", error);
        if (deltaOiProfileCanvas && deltaOiProfileCanvas.getContext) {
          const ctx = deltaOiProfileCanvas.getContext("2d");
          if (ctx)
            ctx.clearRect(
              0,
              0,
              deltaOiProfileCanvas.width,
              deltaOiProfileCanvas.height,
            );
        }
      }
    },
  };

  const globalHelpers = {
    subscribeToBarUpdates(profile, symbol) {
      if (!window.eventBus || !symbol || !profile) return;
      if (profile._unsubscribeBar) {
        profile._unsubscribeBar();
        profile._unsubscribeBar = null;
      }
      // Subscribe to 5-minute bar events for the symbol
      // Note: These events may not be published consistently - relying on direct updates instead
      profile._unsubscribeBar = window.eventBus.subscribe(
        `bar-${symbol}-5m`,
        () => {
          manager.updateProfile(symbol);
        },
      );
    },
    unsubscribeFromBarUpdates(profile) {
      if (profile && profile._unsubscribeBar) {
        profile._unsubscribeBar();
        profile._unsubscribeBar = null;
      }
    },
    applyProfileLines(lines) {
      if (!lines || isNaN(parseInt(lines))) return;
      const lineCount = parseInt(lines) * DEFAULT_CONFIG.priceRangeMultiplier;
      localStorage.setItem("volumeProfileLines", lineCount.toString());
      const states = window.chartStates;
      if (!states) return;
      states.forEach((state) => {
        if (!state) return;
        if (state.deltaOiProfile && state.deltaOiProfile.config) {
          state.deltaOiProfile.config.priceRange = lineCount;
          if (typeof state.deltaOiProfile.update === "function") {
            state.deltaOiProfile.update();
          }
        }
      });
    },
    cleanupAllProfiles() {
      const states = window.chartStates;
      if (!states) return;
      states.forEach((state) => {
        if (!state) return;
        if (state.deltaOiProfile) {
          globalHelpers.unsubscribeFromBarUpdates(state.deltaOiProfile);
          if (typeof state.deltaOiProfile.cleanup === "function") {
            state.deltaOiProfile.cleanup();
          }
        }
      });
    },
    initializeProfiles(state) {
      if (
        !state.config ||
        !state.config.ticker ||
        !state.config.ticker.symbol
      ) {
        console.warn(
          "ΔOI Profile: ticker not set, skipping profile initialization.",
        );
        return state;
      }
      const savedVPLines = localStorage.getItem("volumeProfileLines");
      const defaultVPLines = savedVPLines
        ? parseInt(savedVPLines) * DEFAULT_CONFIG.priceRangeMultiplier
        : 600;

      const commonConfig = {
        priceRange: defaultVPLines,
        position: 0.1,
        alignLeft: true,
        profileWidth: 80,
        colors: {
          forcedClose: "rgba(255, 0, 0, 0.7)",
          neutral: "rgba(150, 150, 150, 0.5)",
        },
        visible: true,
        liveUpdate: true,
        maxBars: 12000,
        barWidth: 1.0,
        showMedian: false,
      };

      if (
        !state.deltaOiProfile &&
        window.deltaOiProfileManager &&
        window.deltaOiProfileManager.initialize
      ) {
        try {
          state.deltaOiProfile = window.deltaOiProfileManager.initialize(
            state,
            commonConfig,
          );
        } catch (error) {
          console.warn("Failed to initialize ΔOI profile:", error);
        }
      }
      if (
        state.config &&
        state.config.ticker &&
        state.config.ticker.symbol &&
        state.deltaOiProfile
      ) {
        // Subscribe to bar updates for this profile
        globalHelpers.subscribeToBarUpdates(
          state.deltaOiProfile,
          state.config.ticker.symbol,
        );
      }
      return state;
    },
    handleChartSwitch(state) {
      this.cleanupAllProfiles();
      this.initializeProfiles(state);
    },
    switchToPair(pair) {
      const state = window.chartStates?.get(pair);
      if (state) {
        this.handleChartSwitch(state);
      }
      return Promise.resolve();
    },
  };

  const manager = {
    updateProfile(symbol) {
      const profile = profiles.get(symbol);
      if (!profile || !profile.dataLoaded) {
        return;
      }

      const { chartState, config } = profile;
      const updateProfileData = () => {
        try {
          const currentPriceRange = config.priceRange;
          const recentData = chartState.data.priceData
            .slice(-config.maxBars)
            .map((bar) => ({ ...bar }));
          const openInterestData = (chartState.data.openInterestData || []).map(
            (point) => ({ ...point }),
          );
          if (!recentData.length) return;

          const newProfileData = calculator.calculateDeltaOiProfile(
            recentData,
            openInterestData,
            { ...config },
          );
          if (newProfileData && newProfileData.levels) {
            profile.data = newProfileData;
            config.priceRange = currentPriceRange;
            profile.isDirty = true; // Set dirty flag
            // renderer.drawDeltaOiProfile(profile); // Drawing will be handled by redrawLoop
          }
        } catch (error) {
          console.error("Error updating delta OI profile:", error);
        }
      };

      if (!profile.debouncedUpdate)
        profile.debouncedUpdate = utils.debounce(updateProfileData, 200);
      if (window.requestIdleCallback) {
        window.requestIdleCallback(() => updateProfileData(), {
          timeout: 1000,
        });
      } else {
        setTimeout(updateProfileData, 0);
      }
    },
    toggleVisibility(symbol) {
      const profile = profiles.get(symbol);
      if (!profile) return false;
      profile.visible = !profile.visible;
      profile.isDirty = true; // Set dirty flag
      // renderer.drawDeltaOiProfile(profile); // Drawing will be handled by redrawLoop
      return profile.visible;
    },
    initialize(chartState, config = {}) {
      if (
        !chartState.config ||
        !chartState.config.ticker ||
        !chartState.config.ticker.symbol
      ) {
        console.warn(
          "DeltaOiProfile: ticker not set, skipping initialization.",
        );
        return null;
      }
      if (profiles.has(chartState.config.ticker.symbol)) {
        return {
          update: () => this.updateProfile(chartState.config.ticker.symbol),
          toggle: () => this.toggleVisibility(chartState.config.ticker.symbol),
          cleanup: () => {
            const profile = profiles.get(chartState.config.ticker.symbol);
            if (profile && profile.cleanup) profile.cleanup();
          },
        };
      }

      const mergedConfig = { ...DEFAULT_CONFIG, ...config };
      const {
        chartContainer,
        chart: { priceChart, priceSeries },
      } = chartState;

      const profile = {
        config: mergedConfig,
        chartState,
        data: null,
        visible: mergedConfig.visible,
        isDirty: true, // Initialize isDirty flag
        priceRangeCache: {
          topPrice: null,
          bottomPrice: null,
          height: null,
          timestamp: Date.now(),
        },
        lastCandleTime:
          Math.floor(Date.now() / 1000 / chartState.config.barInterval) *
          chartState.config.barInterval,
        dataLoaded: false,
      };

      profiles.set(chartState.config.ticker.symbol, profile);
      chartState.data.openInterestData = [];

      let deltaOiProfileCanvas = document.getElementById(
        "delta-oi-profile-canvas",
      );
      if (!deltaOiProfileCanvas) {
        deltaOiProfileCanvas = document.createElement("canvas");
        deltaOiProfileCanvas.id = "delta-oi-profile-canvas";
        Object.assign(deltaOiProfileCanvas.style, {
          position: "absolute",
          top: "0",
          left: "0",
          pointerEvents: "none",
          zIndex: "5",
        });
        chartContainer.appendChild(deltaOiProfileCanvas);
      }
      profile.deltaOiProfileCanvas = deltaOiProfileCanvas;

      setTimeout(() => {
        const oiCanvas = chartContainer.querySelector(
          "#open-interest-profile-canvas",
        );
        if (oiCanvas) {
          oiCanvas.style.left = "80px";
          oiCanvas.style.position = "absolute";
          oiCanvas.style.zIndex = "5";
        }
      }, 0);

      const chartCanvas = chartContainer.querySelector("canvas");
      if (!chartCanvas) return null;
      profile.chartCanvas = chartCanvas;

      profile.drawDeltaOiProfile = () => renderer.drawDeltaOiProfile(profile);
      const debouncedDraw = utils.debounce(profile.drawDeltaOiProfile, 50);

      try {
        const resizeObserver = new ResizeObserver(() => {
          profile.priceRangeCache.timestamp = 0;
          profile.isDirty = true; // Set dirty flag
          debouncedDraw();
        });
        resizeObserver.observe(chartCanvas);
        resizeObserver.observe(chartContainer);
        profile.resizeObserver = resizeObserver;
      } catch {
        const windowResizeHandler = () => {
          profile.priceRangeCache.timestamp = 0;
          profile.isDirty = true; // Set dirty flag
          debouncedDraw();
        };
        window.addEventListener("resize", windowResizeHandler);
        profile.windowResizeHandler = windowResizeHandler;
      }

      const chartInteractionHandler = () => {
        profile.isDirty = true; // Set dirty flag
        debouncedDraw();
      };
      chartCanvas.addEventListener("mousemove", chartInteractionHandler);
      chartCanvas.addEventListener("click", chartInteractionHandler);
      profile.chartInteractionHandler = chartInteractionHandler;

      if (priceChart?.timeScale) {
        try {
          profile.timeScaleUnsubscribe = priceChart
            .timeScale()
            .subscribeVisibleTimeRangeChange(() => {
              profile.priceRangeCache.timestamp = 0;
              profile.isDirty = true; // Set dirty flag
              debouncedDraw();
            });
        } catch {}
      }

      // Subscribe to price scale changes for vertical resize/zoom
      if (priceChart && typeof priceChart.priceScale === 'function') {
        try {
          const rightScale = priceChart.priceScale('right');
          if (rightScale && typeof rightScale.subscribeVisiblePriceRangeChange === 'function') {
            profile.priceScaleUnsubscribe = rightScale.subscribeVisiblePriceRangeChange(() => {
              profile.priceRangeCache.timestamp = 0;
              profile.isDirty = true;
              debouncedDraw();
            });
          }
        } catch {}
      }

      // Fallback: MutationObserver to force overlay canvas resize if chart canvas changes
      try {
        const mo = new MutationObserver(() => {
          if (profile.chartCanvas && profile.deltaOiProfileCanvas) {
            if (
              profile.chartCanvas.width !== profile.deltaOiProfileCanvas.width ||
              profile.chartCanvas.height !== profile.deltaOiProfileCanvas.height
            ) {
              profile.deltaOiProfileCanvas.width = profile.chartCanvas.width;
              profile.deltaOiProfileCanvas.height = profile.chartCanvas.height;
              profile.priceRangeCache.timestamp = 0;
              profile.isDirty = true;
              debouncedDraw();
            }
          }
        });
        mo.observe(profile.chartCanvas, { attributes: true, attributeFilter: ['width', 'height'] });
        profile.mutationObserver = mo;
      } catch {}

      if (mergedConfig.liveUpdate) {
        const originalUpdate =
          chartState.throttledFunctions.throttledPriceUpdate;
        chartState.throttledFunctions.throttledPriceUpdate = function (bar) {
          originalUpdate.call(this, bar);
          const profile = profiles.get(chartState.config.ticker.symbol);
          if (!profile) return;
          const barInterval = chartState.config.barInterval || 300;
          const currentCandleTime =
            Math.floor(bar.time / barInterval) * barInterval;
          if (currentCandleTime > profile.lastCandleTime) {
            profile.lastCandleTime = currentCandleTime;

            // Publish bar close event for consistency with other indicators
            if (window.eventBus) {
              window.eventBus.publish(`bar-${chartState.config.ticker.symbol}-5m`, {
                time: currentCandleTime,
                symbol: chartState.config.ticker.symbol
              });
            }

            const currentPriceRange = profile.config.priceRange;
            try {
              const priceDataCopy = chartState.data.priceData
                .slice(-profile.config.maxBars)
                .map((bar) => ({ ...bar }));
              const openInterestDataCopy = (
                chartState.data.openInterestData || []
              ).map((point) => ({ ...point }));
              const newProfileData = calculator.calculateDeltaOiProfile(
                priceDataCopy,
                openInterestDataCopy,
                { ...profile.config },
              );
              if (newProfileData && newProfileData.levels) {
                profile.data = newProfileData;
                profile.config.priceRange = currentPriceRange;
                profile.isDirty = true; // Set dirty flag
                // renderer.drawDeltaOiProfile(profile); // Drawing will be handled by redrawLoop
              }
            } catch (error) {
              console.error(
                "Error recalculating negative delta OI profile:",
                error,
              );
              profile.config.priceRange = currentPriceRange;
            }
          } else if (mergedConfig.liveUpdate) {
            if (profile.debouncedUpdate) profile.debouncedUpdate();
            else this.updateProfile(chartState.config.ticker.symbol);
          }
        };
      }

      setTimeout(() => {
        if (!profile.dataLoaded) renderer.drawDeltaOiProfile(profile);
      }, 100);

      function redrawLoop() {
        if (profile.isDirty) {
          if (!profile.dataLoaded) renderer.drawDeltaOiProfile(profile); // Still draw loading state if dirty
          else renderer.drawDeltaOiProfile(profile);
          profile.isDirty = false;
        }
        profile.redrawFrameId = requestAnimationFrame(redrawLoop);
      }
      profile.redrawFrameId = requestAnimationFrame(redrawLoop);

      dataFetcher.fetchDataInParallel(profile);

      // Subscribe to bar updates for this profile
      globalHelpers.subscribeToBarUpdates(profile, chartState.config.ticker.symbol);

      // Listen for Bybit OI REST poller updates and update openInterestData in all profiles
      if (window.PS && window.PS.bybitOpenInterestBars) {
        function updateAllDeltaOIProfilesWithLiveOI() {
          const states = window.chartStates;
          if (!states) return;
          const oiBars = (window.PS.bybitOpenInterestBars || []);
          if (!oiBars.length) return; // Guard: skip if no OI data
          states.forEach(state => {
            if (!state || !state.deltaOiProfile) return;
            // Use the latest OI bars from the poller
            state.data.openInterestData = oiBars.map(bar => ({
              time: bar.time,
              openInterest: bar.openInterest,
              // Optionally, match price to closest bar
              price: (state.data.priceData || []).find(b => b.time === bar.time)?.close || null,
              priceChange: 0,
              buyFlow: 0,
              sellFlow: 0,
              hasOrderFlow: false
            }));
            // Trigger profile update
            if (state.deltaOiProfile.update) state.deltaOiProfile.update();
          });
        }
        // Listen for poller updates (poller runs every 10s)
        setInterval(updateAllDeltaOIProfilesWithLiveOI, 10000);
      }

      return {
        update: () => this.updateProfile(chartState.config.ticker.symbol),
        toggle: () => this.toggleVisibility(chartState.config.ticker.symbol),
        config: profile.config,
        cleanup: () => {
          if (profile.redrawFrameId)
            cancelAnimationFrame(profile.redrawFrameId);
          if (profile.resizeObserver) profile.resizeObserver.disconnect();
          if (profile.windowResizeHandler)
            window.removeEventListener("resize", profile.windowResizeHandler);
          if (profile.chartInteractionHandler && profile.chartCanvas) {
            profile.chartCanvas.removeEventListener(
              "mousemove",
              profile.chartInteractionHandler,
            );
            profile.chartCanvas.removeEventListener(
              "click",
              profile.chartInteractionHandler,
            );
          }
          if (profile.timeScaleUnsubscribe) profile.timeScaleUnsubscribe();
          if (profile.priceScaleUnsubscribe) profile.priceScaleUnsubscribe();
          if (profile.deltaOiProfileCanvas)
            profile.deltaOiProfileCanvas.remove();
          if (profile.mutationObserver) profile.mutationObserver.disconnect();
          // Unsubscribe from bar updates
          globalHelpers.unsubscribeFromBarUpdates(profile);
          profiles.delete(chartState.config.ticker.symbol);
        },
      };
    },
  };

  return {
    initialize: manager.initialize,
    updateProfile: manager.updateProfile,
    toggleVisibility: manager.toggleVisibility,
    applyProfileLines: globalHelpers.applyProfileLines,
    cleanupAllProfiles: globalHelpers.cleanupAllProfiles,
    initializeProfiles: globalHelpers.initializeProfiles,
    handleChartSwitch: globalHelpers.handleChartSwitch,
    switchToPair: globalHelpers.switchToPair,
  };
})();
